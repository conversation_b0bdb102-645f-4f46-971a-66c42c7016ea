# Salary Slip Approval Report Setup Guide

This guide will help you create a script report for salary slips with approval functionality in Frappe Desk.

## Features

The report includes:
- **Employee Details**: First Name, Last Name, Designation
- **Salary Components**: Basic, Overtime, Gross Pay, Net Pay
- **Approval Buttons**: 
  - Approve Selected
  - Approve All Draft
  - Reject Selected
  - Submit Selected (for CEO/authorized users)

## Setup Instructions

### Step 1: Create Script Report

1. Go to **Report List** in Frappe Desk
2. Click **New** to create a new report
3. Fill in the following details:
   - **Report Name**: `Salary Slip Approval Report`
   - **Report Type**: `Script Report`
   - **Module**: `Payroll` (or your preferred module)
   - **Is Standard**: Leave unchecked

### Step 2: Add the Python Script

In the **Script** section, paste the following code:

```python
import frappe
from frappe import _
from frappe.utils import flt

def execute(filters=None):
    columns = get_columns()
    data = get_data(filters)
    return columns, data

def get_columns():
    return [
        {
            "fieldname": "name",
            "label": _("Salary Slip"),
            "fieldtype": "Link",
            "options": "Salary Slip",
            "width": 120
        },
        {
            "fieldname": "employee",
            "label": _("Employee"),
            "fieldtype": "Link",
            "options": "Employee",
            "width": 100
        },
        {
            "fieldname": "first_name",
            "label": _("First Name"),
            "fieldtype": "Data",
            "width": 100
        },
        {
            "fieldname": "last_name",
            "label": _("Last Name"),
            "fieldtype": "Data",
            "width": 100
        },
        {
            "fieldname": "designation",
            "label": _("Designation"),
            "fieldtype": "Data",
            "width": 120
        },
        {
            "fieldname": "basic",
            "label": _("Basic"),
            "fieldtype": "Currency",
            "width": 100
        },
        {
            "fieldname": "overtime",
            "label": _("Overtime"),
            "fieldtype": "Currency",
            "width": 100
        },
        {
            "fieldname": "gross_pay",
            "label": _("Gross Pay"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "net_pay",
            "label": _("Net Pay"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "status",
            "label": _("Status"),
            "fieldtype": "Data",
            "width": 100
        },
        {
            "fieldname": "workflow_state",
            "label": _("Workflow State"),
            "fieldtype": "Data",
            "width": 120
        }
    ]

def get_data(filters):
    conditions = get_conditions(filters)
    
    # Get salary slips with employee details
    salary_slips = frappe.db.sql("""
        SELECT 
            ss.name,
            ss.employee,
            ss.designation,
            ss.gross_pay,
            ss.net_pay,
            ss.status,
            ss.workflow_state,
            emp.first_name,
            emp.last_name,
            emp.designation as emp_designation
        FROM `tabSalary Slip` ss
        LEFT JOIN `tabEmployee` emp ON ss.employee = emp.name
        WHERE ss.docstatus = 0 {conditions}
        ORDER BY ss.creation DESC
    """.format(conditions=conditions), as_dict=1)
    
    # Get salary components for each slip
    for slip in salary_slips:
        # Get Basic salary component
        basic_amount = frappe.db.sql("""
            SELECT amount FROM `tabSalary Detail`
            WHERE parent = %s AND parentfield = 'earnings' 
            AND salary_component IN ('Basic', 'Basic Salary', 'Base')
            LIMIT 1
        """, slip.name)
        slip.basic = flt(basic_amount[0][0]) if basic_amount else 0
        
        # Get Overtime component
        overtime_amount = frappe.db.sql("""
            SELECT amount FROM `tabSalary Detail`
            WHERE parent = %s AND parentfield = 'earnings' 
            AND salary_component IN ('Overtime', 'Over Time', 'OT')
            LIMIT 1
        """, slip.name)
        slip.overtime = flt(overtime_amount[0][0]) if overtime_amount else 0
        
        # Use employee designation if salary slip designation is empty
        if not slip.designation:
            slip.designation = slip.emp_designation
    
    return salary_slips

def get_conditions(filters):
    conditions = ""
    
    if filters.get("company"):
        conditions += " AND ss.company = %(company)s"
    
    if filters.get("department"):
        conditions += " AND ss.department = %(department)s"
    
    if filters.get("from_date"):
        conditions += " AND ss.start_date >= %(from_date)s"
    
    if filters.get("to_date"):
        conditions += " AND ss.end_date <= %(to_date)s"
    
    if filters.get("employee"):
        conditions += " AND ss.employee = %(employee)s"
    
    return conditions
```

### Step 3: Add Client Script

In the **Client Script** section, paste the JavaScript code from the next section.

## Client Script Code

```javascript
frappe.query_reports["Salary Slip Approval Report"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        },
        {
            "fieldname": "department",
            "label": __("Department"),
            "fieldtype": "Link",
            "options": "Department"
        },
        {
            "fieldname": "employee",
            "label": __("Employee"),
            "fieldtype": "Link",
            "options": "Employee"
        }
    ],
    
    "onload": function(report) {
        // Add custom buttons for approval actions
        report.page.add_inner_button(__("Approve Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to approve"));
                return;
            }
            approve_salary_slips(selected_docs, "approve");
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Approve All Draft"), function() {
            approve_all_draft_slips();
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Reject Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to reject"));
                return;
            }
            approve_salary_slips(selected_docs, "reject");
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Submit Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to submit"));
                return;
            }
            submit_salary_slips(selected_docs);
        }, __("Workflow Actions"));
    }
};

function approve_salary_slips(selected_docs, action) {
    let slip_names = selected_docs.map(d => d.name);
    
    frappe.confirm(
        __("Are you sure you want to {0} {1} salary slip(s)?", [action, slip_names.length]),
        function() {
            frappe.call({
                method: "frappe.client.set_value",
                args: {
                    doctype: "Salary Slip",
                    name: slip_names,
                    fieldname: "workflow_state",
                    value: action === "approve" ? "Approved" : "Rejected"
                },
                callback: function(r) {
                    if (!r.exc) {
                        frappe.msgprint(__("{0} salary slip(s) {1}d successfully", [slip_names.length, action]));
                        frappe.query_report.refresh();
                    }
                }
            });
        }
    );
}

function approve_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to approve all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: {
                        docstatus: 0,
                        workflow_state: ["in", ["Draft", "Pending", "Open"]]
                    },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        
                        frappe.call({
                            method: "frappe.client.set_value",
                            args: {
                                doctype: "Salary Slip",
                                name: slip_names,
                                fieldname: "workflow_state",
                                value: "Approved"
                            },
                            callback: function(r) {
                                if (!r.exc) {
                                    frappe.msgprint(__("{0} salary slip(s) approved successfully", [slip_names.length]));
                                    frappe.query_report.refresh();
                                }
                            }
                        });
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

function submit_salary_slips(selected_docs) {
    let slip_names = selected_docs.map(d => d.name);
    
    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [slip_names.length]),
        function() {
            let submitted_count = 0;
            let total_count = slip_names.length;
            
            slip_names.forEach(function(slip_name) {
                frappe.call({
                    method: "frappe.client.submit_doc",
                    args: {
                        doc: {
                            doctype: "Salary Slip",
                            name: slip_name
                        }
                    },
                    callback: function(r) {
                        submitted_count++;
                        if (submitted_count === total_count) {
                            frappe.msgprint(__("{0} salary slip(s) submitted successfully", [total_count]));
                            frappe.query_report.refresh();
                        }
                    }
                });
            });
        }
    );
}
```

## Usage Instructions

1. **Save** the report after adding both scripts
2. **Navigate** to the report from the Report List
3. **Apply filters** as needed (Company, Date Range, Department, Employee)
4. **Select salary slips** using checkboxes
5. **Use workflow buttons**:
   - **Approve Selected**: Approve chosen salary slips
   - **Approve All Draft**: Approve all draft slips at once
   - **Reject Selected**: Reject chosen salary slips
   - **Submit Selected**: Submit approved slips (final step)

## Important Notes

- Only draft salary slips (docstatus = 0) are shown
- The report automatically detects Basic and Overtime components
- Workflow states are updated based on your system's configuration
- CEO/authorized users can submit salary slips after approval
- The report refreshes automatically after each action

## Customization

You can modify the salary component names in the script if your system uses different names:
- Change `'Basic', 'Basic Salary', 'Base'` to match your basic salary component
- Change `'Overtime', 'Over Time', 'OT'` to match your overtime component

## Permissions

Ensure users have appropriate permissions for:
- Reading Salary Slip documents
- Updating workflow states
- Submitting documents (for final approval)
