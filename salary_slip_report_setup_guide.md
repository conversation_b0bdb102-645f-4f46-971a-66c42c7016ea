# Salary Slip Approval Report Setup Guide

This guide will help you create a script report for salary slips with submit functionality in Frappe Desk.

## Features

The report includes:
- **Employee Details**: First Name, Last Name, Designation
- **Salary Components**: Basic, Overtime, Gross Pay, Net Pay
- **Submit Buttons**:
  - Submit Selected (submit chosen salary slips)
  - Submit All Draft (submit all draft slips at once)

## Setup Instructions

### Step 1: Create Script Report

1. Go to **Report List** in Frappe Desk
2. Click **New** to create a new report
3. Fill in the following details:
   - **Report Name**: `Salary Slip Approval Report`
   - **Report Type**: `Script Report`
   - **Module**: `Payroll` (or your preferred module)
   - **Is Standard**: Leave unchecked

### Step 2: Add the Python Script

In the **Script** section, paste the following code:

```python
import frappe
from frappe.utils import flt

# Define columns
columns = [
    {
        "fieldname": "name",
        "label": "Salary Slip",
        "fieldtype": "Link",
        "options": "Salary Slip",
        "width": 120
    },
    {
        "fieldname": "employee",
        "label": "Employee",
        "fieldtype": "Link",
        "options": "Employee",
        "width": 100
    },
    {
        "fieldname": "first_name",
        "label": "First Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "last_name",
        "label": "Last Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "designation",
        "label": "Designation",
        "fieldtype": "Data",
        "width": 120
    },
    {
        "fieldname": "basic",
        "label": "Basic",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "overtime",
        "label": "Overtime",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "gross_pay",
        "label": "Gross Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "net_pay",
        "label": "Net Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "status",
        "label": "Status",
        "fieldtype": "Data",
        "width": 100
    }
]

# Get salary slips with employee details
salary_slips = frappe.db.sql("""
    SELECT
        ss.name,
        ss.employee,
        ss.designation,
        ss.gross_pay,
        ss.net_pay,
        ss.status,
        emp.first_name,
        emp.last_name,
        emp.designation as emp_designation
    FROM `tabSalary Slip` ss
    LEFT JOIN `tabEmployee` emp ON ss.employee = emp.name
    WHERE ss.docstatus = 0
    ORDER BY ss.creation DESC
    LIMIT 100
""", as_dict=1)

# Get salary components for each slip
for slip in salary_slips:
    # Get Basic salary component
    basic_amount = frappe.db.sql("""
        SELECT amount FROM `tabSalary Detail`
        WHERE parent = %s AND parentfield = 'earnings'
        AND salary_component IN ('Basic', 'Basic Salary', 'Base')
        LIMIT 1
    """, slip.name)
    slip.basic = flt(basic_amount[0][0]) if basic_amount else 0

    # Get Overtime component
    overtime_amount = frappe.db.sql("""
        SELECT amount FROM `tabSalary Detail`
        WHERE parent = %s AND parentfield = 'earnings'
        AND salary_component IN ('Overtime', 'Over Time', 'OT')
        LIMIT 1
    """, slip.name)
    slip.overtime = flt(overtime_amount[0][0]) if overtime_amount else 0

    # Use employee designation if salary slip designation is empty
    if not slip.designation:
        slip.designation = slip.emp_designation

# Assign data for Frappe Desk
data = salary_slips
```

### Step 3: Add Client Script

In the **Client Script** section, paste the JavaScript code from the next section.

## Client Script Code

```javascript
frappe.query_reports["Salary Slip Approval Report"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        },
        {
            "fieldname": "department",
            "label": __("Department"),
            "fieldtype": "Link",
            "options": "Department"
        },
        {
            "fieldname": "employee",
            "label": __("Employee"),
            "fieldtype": "Link",
            "options": "Employee"
        }
    ],

    "onload": function(report) {
        // Add submit button for salary slips
        report.page.add_inner_button(__("Submit Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to submit"));
                return;
            }
            submit_salary_slips(selected_docs);
        });

        report.page.add_inner_button(__("Submit All Draft"), function() {
            submit_all_draft_slips();
        });
    }
};

function submit_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to submit all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: {
                        docstatus: 0
                    },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        submit_multiple_slips(slip_names);
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

function submit_salary_slips(selected_docs) {
    let slip_names = selected_docs.map(d => d.name);

    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [slip_names.length]),
        function() {
            submit_multiple_slips(slip_names);
        }
    );
}

function submit_multiple_slips(slip_names) {
    let submitted_count = 0;
    let failed_count = 0;
    let total_count = slip_names.length;

    frappe.show_progress(__("Submitting Salary Slips"), 0, total_count, __("Please wait..."));

    slip_names.forEach(function(slip_name) {
        frappe.call({
            method: "frappe.client.submit_doc",
            args: {
                doc: {
                    doctype: "Salary Slip",
                    name: slip_name
                }
            },
            callback: function(r) {
                if (r.exc) {
                    failed_count++;
                } else {
                    submitted_count++;
                }

                frappe.show_progress(__("Submitting Salary Slips"), submitted_count + failed_count, total_count);

                if (submitted_count + failed_count === total_count) {
                    frappe.hide_progress();

                    let message = __("{0} salary slip(s) submitted successfully", [submitted_count]);
                    if (failed_count > 0) {
                        message += __("<br>{0} salary slip(s) failed to submit", [failed_count]);
                    }

                    frappe.msgprint({
                        message: message,
                        indicator: failed_count > 0 ? 'orange' : 'green'
                    });

                    frappe.query_report.refresh();
                }
            }
        });
    });
}
```

## Usage Instructions

1. **Save** the report after adding both scripts
2. **Navigate** to the report from the Report List
3. **Apply filters** as needed (Company, Date Range, Department, Employee)
4. **Select salary slips** using checkboxes
5. **Use submit buttons**:
   - **Submit Selected**: Submit chosen salary slips
   - **Submit All Draft**: Submit all draft slips at once

## Important Notes

- Only draft salary slips (docstatus = 0) are shown
- The report automatically detects Basic and Overtime components
- Progress bar shows submission status for multiple slips
- Failed submissions are reported separately
- The report refreshes automatically after each action
- No workflow states are required - direct submission

## Customization

You can modify the salary component names in the script if your system uses different names:
- Change `'Basic', 'Basic Salary', 'Base'` to match your basic salary component
- Change `'Overtime', 'Over Time', 'OT'` to match your overtime component

## Permissions

Ensure users have appropriate permissions for:
- Reading Salary Slip documents
- Submitting Salary Slip documents
