// Client Script for Salary Slip Approval Report
// Paste this code into the Client Script section of your Script Report in Frappe Desk

frappe.query_reports["Salary Slip Approval Report"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        },
        {
            "fieldname": "department",
            "label": __("Department"),
            "fieldtype": "Link",
            "options": "Department"
        },
        {
            "fieldname": "employee",
            "label": __("Employee"),
            "fieldtype": "Link",
            "options": "Employee"
        }
    ],
    
    "onload": function(report) {
        // Add custom buttons for approval actions
        report.page.add_inner_button(__("Approve Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to approve"));
                return;
            }
            approve_salary_slips(selected_docs, "approve");
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Approve All Draft"), function() {
            approve_all_draft_slips();
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Reject Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to reject"));
                return;
            }
            approve_salary_slips(selected_docs, "reject");
        }, __("Workflow Actions"));
        
        report.page.add_inner_button(__("Submit Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to submit"));
                return;
            }
            submit_salary_slips(selected_docs);
        }, __("Workflow Actions"));
    }
};

function approve_salary_slips(selected_docs, action) {
    let slip_names = selected_docs.map(d => d.name);
    
    frappe.confirm(
        __("Are you sure you want to {0} {1} salary slip(s)?", [action, slip_names.length]),
        function() {
            frappe.call({
                method: "frappe.client.set_value",
                args: {
                    doctype: "Salary Slip",
                    name: slip_names,
                    fieldname: "workflow_state",
                    value: action === "approve" ? "Approved" : "Rejected"
                },
                callback: function(r) {
                    if (!r.exc) {
                        frappe.msgprint(__("{0} salary slip(s) {1}d successfully", [slip_names.length, action]));
                        frappe.query_report.refresh();
                    }
                }
            });
        }
    );
}

function approve_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to approve all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: {
                        docstatus: 0,
                        workflow_state: ["in", ["Draft", "Pending", "Open"]]
                    },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        
                        frappe.call({
                            method: "frappe.client.set_value",
                            args: {
                                doctype: "Salary Slip",
                                name: slip_names,
                                fieldname: "workflow_state",
                                value: "Approved"
                            },
                            callback: function(r) {
                                if (!r.exc) {
                                    frappe.msgprint(__("{0} salary slip(s) approved successfully", [slip_names.length]));
                                    frappe.query_report.refresh();
                                }
                            }
                        });
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

function submit_salary_slips(selected_docs) {
    let slip_names = selected_docs.map(d => d.name);
    
    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [slip_names.length]),
        function() {
            let submitted_count = 0;
            let total_count = slip_names.length;
            
            slip_names.forEach(function(slip_name) {
                frappe.call({
                    method: "frappe.client.submit_doc",
                    args: {
                        doc: {
                            doctype: "Salary Slip",
                            name: slip_name
                        }
                    },
                    callback: function(r) {
                        submitted_count++;
                        if (submitted_count === total_count) {
                            frappe.msgprint(__("{0} salary slip(s) submitted successfully", [total_count]));
                            frappe.query_report.refresh();
                        }
                    }
                });
            });
        }
    );
}
