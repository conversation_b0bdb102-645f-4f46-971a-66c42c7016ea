["tabGL", "tabProperty", "tabDocType", "tabSales", "tabHas Role", "tabTag", "tabDocType State", "tabSales Invoice Item", "tabDocType Link", "tabGL Entry", "tabBank Transaction", "tabRoute History", "tabTZ Ward", "tabDocType Action", "tabVehicle Log", "tabCustom", "tabTag Link", "tabAdditional", "tabPortal", "tabPrint Heading", "tabInstalled", "tabPrint", "tabBank", "tabVehicle", "tabReport Filter", "tabComment", "tabBank Account", "tabDocField", "tabEmployee", "tabProperty Setter", "tabRoute", "tabInstalled Application", "tabSalary", "tabSingles", "tabDocPerm", "tabTZ", "tabDeleted", "tabPortal Menu Item", "tabDeleted Document", "tabCustom Field", "tabReport", "tabAdditional Salary", "tabEmployee OT Component", "tabVehicle Service", "tabSalary Slip OT Component", "tabTZ Village", "tabHas"]