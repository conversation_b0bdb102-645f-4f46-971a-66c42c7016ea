# Salary Slip Approval Report for Frappe Desk
# This script should be pasted into the Script section of a Script Report in Frappe Desk

# Define columns
columns = [
    {
        "fieldname": "name",
        "label": "Salary Slip",
        "fieldtype": "Link",
        "options": "Salary Slip",
        "width": 120
    },
    {
        "fieldname": "employee",
        "label": "Employee",
        "fieldtype": "Link",
        "options": "Employee",
        "width": 100
    },
    {
        "fieldname": "first_name",
        "label": "First Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "last_name",
        "label": "Last Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "designation",
        "label": "Designation",
        "fieldtype": "Data",
        "width": 120
    },
    {
        "fieldname": "basic",
        "label": "Basic",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "overtime",
        "label": "Overtime",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "gross_pay",
        "label": "Gross Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "net_pay",
        "label": "Net Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "status",
        "label": "Status",
        "fieldtype": "Data",
        "width": 100
    }
]

# Simple test data first - replace with actual query once working
data = [
    {
        "name": "SAL-SLIP-001",
        "employee": "EMP-001",
        "first_name": "John",
        "last_name": "Doe",
        "designation": "Manager",
        "basic": 50000,
        "overtime": 5000,
        "gross_pay": 60000,
        "net_pay": 55000,
        "status": "Draft"
    },
    {
        "name": "SAL-SLIP-002",
        "employee": "EMP-002",
        "first_name": "Jane",
        "last_name": "Smith",
        "designation": "Developer",
        "basic": 45000,
        "overtime": 3000,
        "gross_pay": 52000,
        "net_pay": 48000,
        "status": "Draft"
    }
]


