# Salary Slip Approval Report for Frappe Desk
# This script should be pasted into the Script section of a Script Report in Frappe Desk

import frappe

# Define columns first (global scope)
columns = [
    {
        "fieldname": "name",
        "label": "Salary Slip",
        "fieldtype": "<PERSON>",
        "options": "Salary Slip",
        "width": 120
    },
    {
        "fieldname": "employee",
        "label": "Employee",
        "fieldtype": "Link",
        "options": "Employee",
        "width": 100
    },
    {
        "fieldname": "first_name",
        "label": "First Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "last_name",
        "label": "Last Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "designation",
        "label": "Designation",
        "fieldtype": "Data",
        "width": 120
    },
    {
        "fieldname": "basic",
        "label": "Basic",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "overtime",
        "label": "Overtime",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "gross_pay",
        "label": "Gross Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "net_pay",
        "label": "Net Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "status",
        "label": "Status",
        "fieldtype": "Data",
        "width": 100
    }
]

# Get salary slips with employee details
salary_slips = frappe.db.sql("""
    SELECT
        ss.name,
        ss.employee,
        ss.designation,
        ss.gross_pay,
        ss.net_pay,
        ss.status,
        emp.first_name,
        emp.last_name,
        emp.designation as emp_designation
    FROM `tabSalary Slip` ss
    LEFT JOIN `tabEmployee` emp ON ss.employee = emp.name
    WHERE ss.docstatus = 0
    ORDER BY ss.creation DESC
    LIMIT 100
""", as_dict=1)

# Get salary components for each slip
for slip in salary_slips:
    # Get Basic salary component
    basic_amount = frappe.db.sql("""
        SELECT amount FROM `tabSalary Detail`
        WHERE parent = %s AND parentfield = 'earnings'
        AND salary_component IN ('Basic', 'Basic Salary', 'Base')
        LIMIT 1
    """, slip.name)
    slip.basic = frappe.utils.flt(basic_amount[0][0]) if basic_amount else 0

    # Get Overtime component
    overtime_amount = frappe.db.sql("""
        SELECT amount FROM `tabSalary Detail`
        WHERE parent = %s AND parentfield = 'earnings'
        AND salary_component IN ('Overtime', 'Over Time', 'OT')
        LIMIT 1
    """, slip.name)
    slip.overtime = frappe.utils.flt(overtime_amount[0][0]) if overtime_amount else 0

    # Use employee designation if salary slip designation is empty
    if not slip.designation:
        slip.designation = slip.emp_designation

# Assign data (this is what Frappe Desk reads)
data = salary_slips


